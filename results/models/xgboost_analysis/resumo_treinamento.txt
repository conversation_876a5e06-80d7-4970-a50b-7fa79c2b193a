RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

MODELO BINÁRIO:
  • Tipo: XGBoost Binário
  • Classes: 0=Venda, 1=Compra
  • Função de perda: logloss
  • Threshold de probabilidade: 0.5 (sinais só gerados se prob > 0.5)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Am<PERSON>ud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 116
  • Acurácia geral: 0.662

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (116):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Segunda
  15. Terca
  16. Quarta
  17. Quinta
  18. Sexta
  19. Mes_1
  20. Mes_2
  21. Mes_3
  22. Mes_4
  23. Mes_5
  24. Mes_6
  25. Mes_7
  26. Mes_8
  27. Mes_9
  28. Mes_10
  29. Mes_11
  30. Mes_12
  31. Quarter_1
  32. Quarter_2
  33. Quarter_3
  34. Quarter_4
  35. Last_Day_Quarter
  36. Parkinson_Volatility
  37. MFI
  38. EMV
  39. EMV_MA
  40. Amihud
  41. Roll_Spread
  42. Hurst
  43. Vol_per_Volume
  44. CMF
  45. AD_Line
  46. VO
  47. Volume_Lag_1
  48. Volume_Lag_2
  49. Volume_Lag_3
  50. Volume_Lag_4
  51. Volume_Lag_5
  52. Spread_Lag_1
  53. Spread_Lag_2
  54. Spread_Lag_3
  55. Spread_Lag_4
  56. Spread_Lag_5
  57. Volatilidade_Lag_1
  58. Volatilidade_Lag_2
  59. Volatilidade_Lag_3
  60. Volatilidade_Lag_4
  61. Volatilidade_Lag_5
  62. Parkinson_Volatility_Lag_1
  63. Parkinson_Volatility_Lag_2
  64. Parkinson_Volatility_Lag_3
  65. Parkinson_Volatility_Lag_4
  66. Parkinson_Volatility_Lag_5
  67. MFI_Lag_1
  68. MFI_Lag_2
  69. MFI_Lag_3
  70. MFI_Lag_4
  71. MFI_Lag_5
  72. EMV_Lag_1
  73. EMV_Lag_2
  74. EMV_Lag_3
  75. EMV_Lag_4
  76. EMV_Lag_5
  77. EMV_MA_Lag_1
  78. EMV_MA_Lag_2
  79. EMV_MA_Lag_3
  80. EMV_MA_Lag_4
  81. EMV_MA_Lag_5
  82. Amihud_Lag_1
  83. Amihud_Lag_2
  84. Amihud_Lag_3
  85. Amihud_Lag_4
  86. Amihud_Lag_5
  87. Roll_Spread_Lag_1
  88. Roll_Spread_Lag_2
  89. Roll_Spread_Lag_3
  90. Roll_Spread_Lag_4
  91. Roll_Spread_Lag_5
  92. Hurst_Lag_1
  93. Hurst_Lag_2
  94. Hurst_Lag_3
  95. Hurst_Lag_4
  96. Hurst_Lag_5
  97. Vol_per_Volume_Lag_1
  98. Vol_per_Volume_Lag_2
  99. Vol_per_Volume_Lag_3
  100. Vol_per_Volume_Lag_4
  101. Vol_per_Volume_Lag_5
  102. CMF_Lag_1
  103. CMF_Lag_2
  104. CMF_Lag_3
  105. CMF_Lag_4
  106. CMF_Lag_5
  107. AD_Line_Lag_1
  108. AD_Line_Lag_2
  109. AD_Line_Lag_3
  110. AD_Line_Lag_4
  111. AD_Line_Lag_5
  112. VO_Lag_1
  113. VO_Lag_2
  114. VO_Lag_3
  115. VO_Lag_4
  116. VO_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.662
  • Distribuição das Predições:
    - Venda: 8429 (49.4%)
    - Compra: 8620 (50.6%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
